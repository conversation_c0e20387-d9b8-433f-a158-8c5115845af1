// STB Image placeholder header
// Replace this with the actual stb_image.h from https://github.com/nothings/stb

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

// Basic STB image function declarations
unsigned char *stbi_load(char const *filename, int *x, int *y, int *channels_in_file, int desired_channels);
void stbi_image_free(void *retval_from_stbi_load);

// Additional commonly used functions
int stbi_info(char const *filename, int *x, int *y, int *comp);
const char *stbi_failure_reason(void);

#ifdef __cplusplus
}
#endif

// Note: This is a placeholder. For production use, download the real stb_image.h
// from https://raw.githubusercontent.com/nothings/stb/master/stb_image.h
