@echo off
echo Building Toolbox Application (Minimal Version)...

REM Check for compiler
where g++ >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: g++ compiler not found!
    echo Please install MinGW-w64 or MSYS2
    echo Download from: https://www.msys2.org/
    pause
    exit /b 1
)

REM Set up directories
if not exist "build" mkdir "build"
if not exist "build\obj" mkdir "build\obj"
if not exist "build\bin" mkdir "build\bin"
if not exist "data" mkdir "data"

REM Compiler settings
set INCLUDES=-Iinclude -Iexternal\glad\include -Iexternal\stb
set CXXFLAGS=-std=c++17 -Wall -Wextra -O3 -DNDEBUG -DTOOLBOX_MINIMAL_BUILD
set LIBS=-lopengl32 -lgdi32 -luser32 -lkernel32 -lshell32 -lole32 -loleaut32 -luuid -ladvapi32 -lcrypt32 -lbcrypt
set LDFLAGS=-static-libgcc -static-libstdc++

echo Creating minimal build without external dependencies...

echo Compiling source files...

REM Compile main source files
g++ %CXXFLAGS% %INCLUDES% -c src\main.cpp -o build\obj\main.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\Toolbox.cpp -o build\obj\Toolbox.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\Window.cpp -o build\obj\Window.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\PasswordManager.cpp -o build\obj\PasswordManager.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\Encryption.cpp -o build\obj\Encryption.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\Calculator.cpp -o build\obj\Calculator.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\FileSearcher.cpp -o build\obj\FileSearcher.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\Utils.cpp -o build\obj\Utils.o
if %errorlevel% neq 0 goto :error

echo Compiling external libraries...

REM Compile GLAD
gcc %CXXFLAGS% %INCLUDES% -c external\glad\src\glad.c -o build\obj\glad.o
if %errorlevel% neq 0 goto :error

REM Compile STB
g++ %CXXFLAGS% %INCLUDES% -c external\stb\stb_image.cpp -o build\obj\stb_image.o
if %errorlevel% neq 0 goto :error

echo Linking application...

REM Link everything together (without GLFW and ImGui for minimal build)
g++ build\obj\*.o %LIBS% %LDFLAGS% -o build\bin\Toolbox.exe
if %errorlevel% neq 0 goto :error

echo Copying to Desktop...
copy "build\bin\Toolbox.exe" "C:\Users\<USER>\Desktop\"
copy "image2vector.svg" "C:\Users\<USER>\Desktop\"

echo.
echo ========================================
echo MINIMAL BUILD SUCCESSFUL!
echo ========================================
echo Executable: C:\Users\<USER>\Desktop\Toolbox.exe
echo Icon: C:\Users\<USER>\Desktop\image2vector.svg
echo.
echo NOTE: This is a minimal build without GUI.
echo For full GUI support, install CMake and run the full build.
echo.
echo To run: C:\Users\<USER>\Desktop\Toolbox.exe
echo.
pause
exit /b 0

:error
echo.
echo ========================================
echo BUILD FAILED!
echo ========================================
echo Check the error messages above.
echo Make sure you have MinGW-w64 or MSYS2 installed.
echo.
pause
exit /b 1
