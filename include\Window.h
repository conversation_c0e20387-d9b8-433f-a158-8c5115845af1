#pragma once

#include <GLFW/glfw3.h>
#include <string>
#include <functional>

namespace Toolbox {
    class Window {
    public:
        Window();
        ~Window();

        bool Initialize(int width, int height, const std::string& title);
        void Shutdown();
        
        bool ShouldClose() const;
        void SwapBuffers();
        void PollEvents();
        
        GLFWwindow* GetHandle() const { return m_window; }
        
        void SetFramebufferSizeCallback(std::function<void(int, int)> callback);
        void SetKeyCallback(std::function<void(int, int, int, int)> callback);
        void SetMouseButtonCallback(std::function<void(int, int, int)> callback);
        
        void GetFramebufferSize(int& width, int& height) const;
        void GetWindowSize(int& width, int& height) const;
        
        void SetWindowIcon(const std::string& iconPath);
        void SetWindowIconFromSVG(const std::string& svgPath);
        
    private:
        GLFWwindow* m_window;
        
        static void FramebufferSizeCallback(GLFWwindow* window, int width, int height);
        static void KeyCallback(GLFWwindow* window, int key, int scancode, int action, int mods);
        static void MouseButtonCallback(GLFWwindow* window, int button, int action, int mods);
        
        std::function<void(int, int)> m_framebufferSizeCallback;
        std::function<void(int, int, int, int)> m_keyCallback;
        std::function<void(int, int, int)> m_mouseButtonCallback;
    };
}
