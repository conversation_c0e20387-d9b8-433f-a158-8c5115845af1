#include "Calculator.h"
#include "Config.h"
#include <cmath>
#include <sstream>
#include <algorithm>
#include <regex>
#include <chrono>
#include <iomanip>

namespace Toolbox {
    Calculator::Calculator() 
        : m_mode(CalculatorMode::Basic)
        , m_memoryValue(0.0)
        , m_useRadians(true)
        , m_hasError(false) {
    }

    Calculator::~Calculator() {
    }

    std::string Calculator::Calculate(const std::string& expression) {
        ClearError();
        
        if (expression.empty()) {
            return "0";
        }
        
        try {
            double result = EvaluateExpression(expression);
            
            if (std::isnan(result) || std::isinf(result)) {
                SetError("Invalid result");
                return "Error";
            }
            
            std::string resultStr = FormatNumber(result);
            
            // Add to history
            if (m_history.size() >= Config::MAX_CALCULATOR_HISTORY) {
                m_history.pop_front();
            }
            
            CalculationHistory historyEntry(expression, resultStr);
            historyEntry.timestamp = GetCurrentTimestamp();
            m_history.push_back(historyEntry);
            
            m_currentResult = resultStr;
            return resultStr;
            
        } catch (const std::exception& e) {
            SetError(e.what());
            return "Error";
        }
    }

    void Calculator::Clear() {
        m_currentExpression.clear();
        m_currentResult = "0";
        ClearError();
    }

    void Calculator::ClearHistory() {
        m_history.clear();
    }

    void Calculator::AppendDigit(char digit) {
        if (digit >= '0' && digit <= '9') {
            m_currentExpression += digit;
            ClearError();
        }
    }

    void Calculator::AppendOperator(const std::string& op) {
        if (!m_currentExpression.empty()) {
            m_currentExpression += op;
            ClearError();
        }
    }

    void Calculator::AppendFunction(const std::string& func) {
        m_currentExpression += func + "(";
        ClearError();
    }

    void Calculator::Backspace() {
        if (!m_currentExpression.empty()) {
            m_currentExpression.pop_back();
            ClearError();
        }
    }

    void Calculator::SetDecimalPoint() {
        // Find the last number in the expression
        size_t lastOpPos = m_currentExpression.find_last_of("+-*/()");
        std::string lastNumber = (lastOpPos == std::string::npos) ? 
            m_currentExpression : m_currentExpression.substr(lastOpPos + 1);
        
        // Only add decimal point if the last number doesn't already have one
        if (lastNumber.find('.') == std::string::npos) {
            if (lastNumber.empty()) {
                m_currentExpression += "0.";
            } else {
                m_currentExpression += ".";
            }
            ClearError();
        }
    }

    void Calculator::MemoryStore() {
        if (!m_currentResult.empty() && m_currentResult != "Error") {
            try {
                m_memoryValue = std::stod(m_currentResult);
            } catch (...) {
                // Ignore errors
            }
        }
    }

    void Calculator::MemoryRecall() {
        m_currentExpression += FormatNumber(m_memoryValue);
        ClearError();
    }

    void Calculator::MemoryAdd() {
        if (!m_currentResult.empty() && m_currentResult != "Error") {
            try {
                m_memoryValue += std::stod(m_currentResult);
            } catch (...) {
                // Ignore errors
            }
        }
    }

    void Calculator::MemorySubtract() {
        if (!m_currentResult.empty() && m_currentResult != "Error") {
            try {
                m_memoryValue -= std::stod(m_currentResult);
            } catch (...) {
                // Ignore errors
            }
        }
    }

    void Calculator::MemoryClear() {
        m_memoryValue = 0.0;
    }

    void Calculator::SetMode(CalculatorMode mode) {
        m_mode = mode;
    }

    double Calculator::EvaluateExpression(const std::string& expression) {
        // Replace constants
        std::string processedExpr = expression;
        std::regex piRegex(R"(\bpi\b)", std::regex_constants::icase);
        std::regex eRegex(R"(\be\b)", std::regex_constants::icase);
        
        processedExpr = std::regex_replace(processedExpr, piRegex, std::to_string(PI));
        processedExpr = std::regex_replace(processedExpr, eRegex, std::to_string(E));
        
        // Tokenize and evaluate
        std::vector<std::string> tokens = TokenizeExpression(processedExpr);
        std::vector<std::string> postfix = ConvertToPostfix(tokens);
        return EvaluatePostfix(postfix);
    }

    std::vector<std::string> Calculator::TokenizeExpression(const std::string& expression) {
        std::vector<std::string> tokens;
        std::string currentToken;
        
        for (size_t i = 0; i < expression.length(); ++i) {
            char c = expression[i];
            
            if (std::isspace(c)) {
                continue;
            }
            
            if (std::isdigit(c) || c == '.') {
                currentToken += c;
            } else {
                if (!currentToken.empty()) {
                    tokens.push_back(currentToken);
                    currentToken.clear();
                }
                
                // Handle multi-character operators and functions
                if (c == '*' && i + 1 < expression.length() && expression[i + 1] == '*') {
                    tokens.push_back("**");
                    ++i;
                } else if (std::isalpha(c)) {
                    // Function name
                    std::string funcName;
                    while (i < expression.length() && std::isalpha(expression[i])) {
                        funcName += expression[i++];
                    }
                    --i; // Back up one character
                    tokens.push_back(funcName);
                } else {
                    tokens.push_back(std::string(1, c));
                }
            }
        }
        
        if (!currentToken.empty()) {
            tokens.push_back(currentToken);
        }
        
        return tokens;
    }

    std::vector<std::string> Calculator::ConvertToPostfix(const std::vector<std::string>& tokens) {
        std::vector<std::string> output;
        std::stack<std::string> operators;
        
        for (const std::string& token : tokens) {
            if (std::isdigit(token[0]) || (token[0] == '.' && token.length() > 1)) {
                output.push_back(token);
            } else if (IsFunction(token)) {
                operators.push(token);
            } else if (token == "(") {
                operators.push(token);
            } else if (token == ")") {
                while (!operators.empty() && operators.top() != "(") {
                    output.push_back(operators.top());
                    operators.pop();
                }
                if (!operators.empty()) {
                    operators.pop(); // Remove the "("
                }
                if (!operators.empty() && IsFunction(operators.top())) {
                    output.push_back(operators.top());
                    operators.pop();
                }
            } else if (IsOperator(token)) {
                while (!operators.empty() && operators.top() != "(" &&
                       (GetOperatorPrecedence(operators.top()) > GetOperatorPrecedence(token) ||
                        (GetOperatorPrecedence(operators.top()) == GetOperatorPrecedence(token) && 
                         IsLeftAssociative(token)))) {
                    output.push_back(operators.top());
                    operators.pop();
                }
                operators.push(token);
            }
        }
        
        while (!operators.empty()) {
            output.push_back(operators.top());
            operators.pop();
        }
        
        return output;
    }

    double Calculator::EvaluatePostfix(const std::vector<std::string>& postfix) {
        std::stack<double> values;
        
        for (const std::string& token : postfix) {
            if (std::isdigit(token[0]) || (token[0] == '.' && token.length() > 1)) {
                values.push(std::stod(token));
            } else if (IsFunction(token)) {
                if (values.empty()) {
                    throw std::runtime_error("Invalid expression");
                }
                double value = values.top();
                values.pop();
                values.push(ApplyFunction(token, value));
            } else if (IsOperator(token)) {
                if (values.size() < 2) {
                    throw std::runtime_error("Invalid expression");
                }
                double b = values.top(); values.pop();
                double a = values.top(); values.pop();
                values.push(ApplyOperator(token, a, b));
            }
        }
        
        if (values.size() != 1) {
            throw std::runtime_error("Invalid expression");
        }
        
        return values.top();
    }

    bool Calculator::IsOperator(const std::string& token) const {
        return token == "+" || token == "-" || token == "*" || token == "/" || 
               token == "**" || token == "%" || token == "^";
    }

    bool Calculator::IsFunction(const std::string& token) const {
        return token == "sin" || token == "cos" || token == "tan" ||
               token == "asin" || token == "acos" || token == "atan" ||
               token == "sinh" || token == "cosh" || token == "tanh" ||
               token == "log" || token == "ln" || token == "sqrt" ||
               token == "abs" || token == "ceil" || token == "floor" ||
               token == "exp" || token == "deg" || token == "rad";
    }

    int Calculator::GetOperatorPrecedence(const std::string& op) const {
        if (op == "+" || op == "-") return 1;
        if (op == "*" || op == "/" || op == "%") return 2;
        if (op == "**" || op == "^") return 3;
        return 0;
    }

    bool Calculator::IsLeftAssociative(const std::string& op) const {
        return op != "**" && op != "^";
    }

    double Calculator::ApplyOperator(const std::string& op, double a, double b) {
        if (op == "+") return a + b;
        if (op == "-") return a - b;
        if (op == "*") return a * b;
        if (op == "/") {
            if (b == 0) throw std::runtime_error("Division by zero");
            return a / b;
        }
        if (op == "**" || op == "^") return std::pow(a, b);
        if (op == "%") return std::fmod(a, b);
        
        throw std::runtime_error("Unknown operator: " + op);
    }

    double Calculator::ApplyFunction(const std::string& func, double value) {
        if (func == "sin") return std::sin(m_useRadians ? value : value * PI / 180.0);
        if (func == "cos") return std::cos(m_useRadians ? value : value * PI / 180.0);
        if (func == "tan") return std::tan(m_useRadians ? value : value * PI / 180.0);
        if (func == "asin") return m_useRadians ? std::asin(value) : std::asin(value) * 180.0 / PI;
        if (func == "acos") return m_useRadians ? std::acos(value) : std::acos(value) * 180.0 / PI;
        if (func == "atan") return m_useRadians ? std::atan(value) : std::atan(value) * 180.0 / PI;
        if (func == "sinh") return std::sinh(value);
        if (func == "cosh") return std::cosh(value);
        if (func == "tanh") return std::tanh(value);
        if (func == "log") return std::log10(value);
        if (func == "ln") return std::log(value);
        if (func == "sqrt") return std::sqrt(value);
        if (func == "abs") return std::abs(value);
        if (func == "ceil") return std::ceil(value);
        if (func == "floor") return std::floor(value);
        if (func == "exp") return std::exp(value);
        if (func == "deg") return value * 180.0 / PI;
        if (func == "rad") return value * PI / 180.0;
        
        throw std::runtime_error("Unknown function: " + func);
    }

    std::string Calculator::FormatNumber(double value) const {
        // Handle special cases
        if (std::isnan(value)) return "NaN";
        if (std::isinf(value)) return value > 0 ? "∞" : "-∞";
        
        // Format with appropriate precision
        std::ostringstream oss;
        
        // Check if it's effectively an integer
        if (std::abs(value - std::round(value)) < 1e-10) {
            oss << std::fixed << std::setprecision(0) << value;
        } else {
            oss << std::setprecision(Config::MAX_DISPLAY_DIGITS) << value;
        }
        
        std::string result = oss.str();
        
        // Remove trailing zeros after decimal point
        if (result.find('.') != std::string::npos) {
            result.erase(result.find_last_not_of('0') + 1, std::string::npos);
            result.erase(result.find_last_not_of('.') + 1, std::string::npos);
        }
        
        return result;
    }

    void Calculator::SetError(const std::string& message) {
        m_hasError = true;
        m_errorMessage = message;
    }

    void Calculator::ClearError() {
        m_hasError = false;
        m_errorMessage.clear();
    }

    long long Calculator::GetCurrentTimestamp() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
    }

    // Number base conversion functions for programmer mode
    std::string Calculator::DecimalToBinary(long long value) const {
        if (value == 0) return "0";
        
        std::string binary;
        bool negative = value < 0;
        if (negative) value = -value;
        
        while (value > 0) {
            binary = (value % 2 ? '1' : '0') + binary;
            value /= 2;
        }
        
        return negative ? "-" + binary : binary;
    }

    std::string Calculator::DecimalToHex(long long value) const {
        std::ostringstream oss;
        oss << std::hex << std::uppercase << value;
        return oss.str();
    }

    std::string Calculator::DecimalToOctal(long long value) const {
        std::ostringstream oss;
        oss << std::oct << value;
        return oss.str();
    }

    long long Calculator::BinaryToDecimal(const std::string& binary) const {
        return std::stoll(binary, nullptr, 2);
    }

    long long Calculator::HexToDecimal(const std::string& hex) const {
        return std::stoll(hex, nullptr, 16);
    }

    long long Calculator::OctalToDecimal(const std::string& octal) const {
        return std::stoll(octal, nullptr, 8);
    }
}
