#pragma once

#include <string>
#include <vector>
#include <map>
#include <memory>

namespace Toolbox {
    struct PasswordEntry {
        std::string title;
        std::string username;
        std::string password;
        std::string url;
        std::string notes;
        std::string category;
        long long createdTime;
        long long modifiedTime;
        
        PasswordEntry() : createdTime(0), modifiedTime(0) {}
    };

    class PasswordManager {
    public:
        PasswordManager();
        ~PasswordManager();

        // Master password management
        bool SetMasterPassword(const std::string& password);
        bool VerifyMasterPassword(const std::string& password);
        bool HasMasterPassword() const;
        void ChangeMasterPassword(const std::string& oldPassword, const std::string& newPassword);

        // Password entry management
        bool AddPassword(const PasswordEntry& entry);
        bool UpdatePassword(const std::string& title, const PasswordEntry& entry);
        bool DeletePassword(const std::string& title);
        std::vector<PasswordEntry> GetAllPasswords() const;
        PasswordEntry* GetPassword(const std::string& title);
        
        // Search and filter
        std::vector<PasswordEntry> SearchPasswords(const std::string& query) const;
        std::vector<PasswordEntry> GetPasswordsByCategory(const std::string& category) const;
        std::vector<std::string> GetAllCategories() const;

        // File operations
        bool LoadFromFile(const std::string& filename);
        bool SaveToFile(const std::string& filename);
        
        // Security
        void Lock();
        bool IsLocked() const { return m_isLocked; }
        
        // Password generation
        static std::string GeneratePassword(int length = 16, 
                                          bool includeUppercase = true,
                                          bool includeLowercase = true, 
                                          bool includeNumbers = true,
                                          bool includeSymbols = true);
        
        // Password strength analysis
        static int CalculatePasswordStrength(const std::string& password);
        static std::string GetPasswordStrengthText(int strength);

    private:
        std::map<std::string, PasswordEntry> m_passwords;
        std::string m_masterPasswordHash;
        std::vector<uint8_t> m_salt;
        std::vector<uint8_t> m_encryptionKey;
        bool m_isLocked;
        bool m_hasMasterPassword;
        
        // Internal helper methods
        bool DeriveEncryptionKey(const std::string& masterPassword);
        std::string SerializePasswords() const;
        bool DeserializePasswords(const std::string& data);
        long long GetCurrentTimestamp() const;
        
        // File format helpers
        struct FileHeader {
            char magic[8];      // "TOOLBOX1"
            uint32_t version;   // File format version
            uint32_t saltSize;  // Size of salt
            uint32_t dataSize;  // Size of encrypted data
        };
    };
}
