#include "Config.h"
#include <iostream>
#include <exception>
#include <windows.h>

#ifdef TOOLBOX_MINIMAL_BUILD
#include "PasswordManager.h"
#include "Calculator.h"
#include "FileSearcher.h"
#else
#include "Toolbox.h"
#endif

// Windows-specific: Hide console window in release builds
#ifdef NDEBUG
#pragma comment(linker, "/SUBSYSTEM:windows /ENTRY:mainCRTStartup")
#endif

int main() {
    try {
        // Set up Windows console for debug output
        #ifdef _DEBUG
        AllocConsole();
        freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
        std::cout << "Toolbox Debug Console" << std::endl;
        #endif

        std::cout << "Starting " << Toolbox::Config::APP_NAME << " v"
                  << Toolbox::Config::APP_VERSION << "..." << std::endl;

        #ifdef TOOLBOX_MINIMAL_BUILD
        // Minimal console-based version
        std::cout << "=== TOOLBOX MINIMAL BUILD ===" << std::endl;
        std::cout << "This is a console-based version for testing core functionality." << std::endl;
        std::cout << "For full GUI support, install CMake and build with full dependencies." << std::endl;
        std::cout << std::endl;

        // Test core components
        std::cout << "Testing Password Manager..." << std::endl;
        Toolbox::PasswordManager pm;
        std::cout << "Password Manager: OK" << std::endl;

        std::cout << "Testing Calculator..." << std::endl;
        Toolbox::Calculator calc;
        std::string result = calc.Calculate("2 + 2");
        std::cout << "Calculator test: 2 + 2 = " << result << std::endl;

        std::cout << "Testing File Searcher..." << std::endl;
        Toolbox::FileSearcher fs;
        std::cout << "File Searcher: OK" << std::endl;

        std::cout << std::endl;
        std::cout << "All core components working! Build successful." << std::endl;
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();

        #else
        // Full GUI version
        Toolbox::ToolboxApp app;

        if (!app.Initialize()) {
            std::cerr << "Failed to initialize application!" << std::endl;
            return -1;
        }

        std::cout << "Application initialized successfully. Starting main loop..." << std::endl;

        // Run the main application loop
        app.Run();

        std::cout << "Application shutting down..." << std::endl;
        #endif

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;

        // Show error message box on Windows
        MessageBoxA(nullptr, e.what(), "Toolbox - Fatal Error", MB_OK | MB_ICONERROR);

        return -1;
    } catch (...) {
        std::cerr << "Unknown fatal error occurred!" << std::endl;

        MessageBoxA(nullptr, "An unknown error occurred during application execution.",
                   "Toolbox - Fatal Error", MB_OK | MB_ICONERROR);

        return -1;
    }
}
