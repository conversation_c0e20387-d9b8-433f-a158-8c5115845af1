#include "Utils.h"
#include <algorithm>
#include <sstream>
#include <regex>
#include <filesystem>
#include <fstream>
#include <random>
#include <iomanip>
#include <windows.h>
#include <shlobj.h>
#include <shellapi.h>

namespace Toolbox {
    namespace Utils {
        std::string Trim(const std::string& str) {
            size_t start = str.find_first_not_of(" \t\n\r\f\v");
            if (start == std::string::npos) return "";
            
            size_t end = str.find_last_not_of(" \t\n\r\f\v");
            return str.substr(start, end - start + 1);
        }

        std::string ToLower(const std::string& str) {
            std::string result = str;
            std::transform(result.begin(), result.end(), result.begin(), ::tolower);
            return result;
        }

        std::string ToUpper(const std::string& str) {
            std::string result = str;
            std::transform(result.begin(), result.end(), result.begin(), ::toupper);
            return result;
        }

        std::vector<std::string> Split(const std::string& str, char delimiter) {
            std::vector<std::string> tokens;
            std::stringstream ss(str);
            std::string token;
            
            while (std::getline(ss, token, delimiter)) {
                tokens.push_back(token);
            }
            
            return tokens;
        }

        std::string Join(const std::vector<std::string>& strings, const std::string& delimiter) {
            if (strings.empty()) return "";
            
            std::ostringstream oss;
            oss << strings[0];
            
            for (size_t i = 1; i < strings.size(); ++i) {
                oss << delimiter << strings[i];
            }
            
            return oss.str();
        }

        bool StartsWith(const std::string& str, const std::string& prefix) {
            return str.length() >= prefix.length() && 
                   str.compare(0, prefix.length(), prefix) == 0;
        }

        bool EndsWith(const std::string& str, const std::string& suffix) {
            return str.length() >= suffix.length() && 
                   str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
        }

        std::string Replace(const std::string& str, const std::string& from, const std::string& to) {
            std::string result = str;
            size_t pos = 0;
            
            while ((pos = result.find(from, pos)) != std::string::npos) {
                result.replace(pos, from.length(), to);
                pos += to.length();
            }
            
            return result;
        }

        bool FileExists(const std::string& path) {
            return std::filesystem::exists(path) && std::filesystem::is_regular_file(path);
        }

        bool DirectoryExists(const std::string& path) {
            return std::filesystem::exists(path) && std::filesystem::is_directory(path);
        }

        bool CreateDirectory(const std::string& path) {
            try {
                return std::filesystem::create_directories(path);
            } catch (const std::exception&) {
                return false;
            }
        }

        std::string GetExecutableDirectory() {
            char buffer[MAX_PATH];
            GetModuleFileNameA(nullptr, buffer, MAX_PATH);
            std::filesystem::path exePath(buffer);
            return exePath.parent_path().string();
        }

        std::string GetUserDocumentsDirectory() {
            char buffer[MAX_PATH];
            if (SHGetFolderPathA(nullptr, CSIDL_MYDOCUMENTS, nullptr, SHGFP_TYPE_CURRENT, buffer) == S_OK) {
                return std::string(buffer);
            }
            return "";
        }

        std::string GetTempDirectory() {
            char buffer[MAX_PATH];
            DWORD result = GetTempPathA(MAX_PATH, buffer);
            if (result > 0 && result <= MAX_PATH) {
                return std::string(buffer);
            }
            return "";
        }

        std::string CombinePaths(const std::string& path1, const std::string& path2) {
            std::filesystem::path p1(path1);
            std::filesystem::path p2(path2);
            return (p1 / p2).string();
        }

        std::string GetCurrentTimeString() {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto tm = *std::localtime(&time_t);
            
            std::ostringstream oss;
            oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
            return oss.str();
        }

        std::string FormatTimestamp(long long timestamp) {
            auto time_t = static_cast<std::time_t>(timestamp);
            auto tm = *std::localtime(&time_t);
            
            std::ostringstream oss;
            oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
            return oss.str();
        }

        long long GetCurrentTimestamp() {
            return std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
        }

        void OpenURL(const std::string& url) {
            ShellExecuteA(nullptr, "open", url.c_str(), nullptr, nullptr, SW_SHOWNORMAL);
        }

        void OpenFileExplorer(const std::string& path) {
            ShellExecuteA(nullptr, "explore", path.c_str(), nullptr, nullptr, SW_SHOWNORMAL);
        }

        std::string GetSystemInfo() {
            std::ostringstream info;
            
            // Get Windows version
            OSVERSIONINFOA osvi;
            ZeroMemory(&osvi, sizeof(OSVERSIONINFOA));
            osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOA);
            
            if (GetVersionExA(&osvi)) {
                info << "Windows " << osvi.dwMajorVersion << "." << osvi.dwMinorVersion;
                info << " Build " << osvi.dwBuildNumber;
                if (strlen(osvi.szCSDVersion) > 0) {
                    info << " " << osvi.szCSDVersion;
                }
                info << "\n";
            }
            
            // Get system info
            SYSTEM_INFO sysInfo;
            GetSystemInfo(&sysInfo);
            info << "Processors: " << sysInfo.dwNumberOfProcessors << "\n";
            
            return info.str();
        }

        bool SetClipboardText(const std::string& text) {
            if (!OpenClipboard(nullptr)) return false;
            
            EmptyClipboard();
            
            HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, text.length() + 1);
            if (!hMem) {
                CloseClipboard();
                return false;
            }
            
            char* pMem = static_cast<char*>(GlobalLock(hMem));
            strcpy_s(pMem, text.length() + 1, text.c_str());
            GlobalUnlock(hMem);
            
            SetClipboardData(CF_TEXT, hMem);
            CloseClipboard();
            
            return true;
        }

        std::string GetClipboardText() {
            if (!OpenClipboard(nullptr)) return "";
            
            HANDLE hData = GetClipboardData(CF_TEXT);
            if (!hData) {
                CloseClipboard();
                return "";
            }
            
            char* pData = static_cast<char*>(GlobalLock(hData));
            std::string result(pData);
            GlobalUnlock(hData);
            CloseClipboard();
            
            return result;
        }

        bool IsValidEmail(const std::string& email) {
            const std::regex emailRegex(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
            return std::regex_match(email, emailRegex);
        }

        bool IsValidURL(const std::string& url) {
            const std::regex urlRegex(R"(^https?://[^\s/$.?#].[^\s]*$)", std::regex_constants::icase);
            return std::regex_match(url, urlRegex);
        }

        bool IsNumeric(const std::string& str) {
            if (str.empty()) return false;
            
            size_t start = 0;
            if (str[0] == '-' || str[0] == '+') start = 1;
            
            bool hasDecimal = false;
            for (size_t i = start; i < str.length(); ++i) {
                if (str[i] == '.') {
                    if (hasDecimal) return false;
                    hasDecimal = true;
                } else if (!std::isdigit(str[i])) {
                    return false;
                }
            }
            
            return start < str.length();
        }

        std::string Base64Encode(const std::vector<uint8_t>& data) {
            const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
            std::string result;
            
            for (size_t i = 0; i < data.size(); i += 3) {
                uint32_t value = data[i] << 16;
                if (i + 1 < data.size()) value |= data[i + 1] << 8;
                if (i + 2 < data.size()) value |= data[i + 2];
                
                result += chars[(value >> 18) & 0x3F];
                result += chars[(value >> 12) & 0x3F];
                result += (i + 1 < data.size()) ? chars[(value >> 6) & 0x3F] : '=';
                result += (i + 2 < data.size()) ? chars[value & 0x3F] : '=';
            }
            
            return result;
        }

        std::vector<uint8_t> Base64Decode(const std::string& encoded) {
            const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
            std::vector<uint8_t> result;
            
            for (size_t i = 0; i < encoded.length(); i += 4) {
                uint32_t value = 0;
                for (int j = 0; j < 4; ++j) {
                    if (i + j < encoded.length() && encoded[i + j] != '=') {
                        size_t pos = chars.find(encoded[i + j]);
                        if (pos != std::string::npos) {
                            value |= pos << (18 - j * 6);
                        }
                    }
                }
                
                result.push_back((value >> 16) & 0xFF);
                if (i + 2 < encoded.length() && encoded[i + 2] != '=') {
                    result.push_back((value >> 8) & 0xFF);
                }
                if (i + 3 < encoded.length() && encoded[i + 3] != '=') {
                    result.push_back(value & 0xFF);
                }
            }
            
            return result;
        }

        std::string GenerateUUID() {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(0, 15);
            
            std::ostringstream oss;
            oss << std::hex;
            
            for (int i = 0; i < 32; ++i) {
                if (i == 8 || i == 12 || i == 16 || i == 20) {
                    oss << "-";
                }
                oss << dis(gen);
            }
            
            return oss.str();
        }

        int GenerateRandomInt(int min, int max) {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(min, max);
            return dis(gen);
        }

        double GenerateRandomDouble(double min, double max) {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_real_distribution<> dis(min, max);
            return dis(gen);
        }

        void SecureZeroMemory(void* ptr, size_t size) {
            SecureZeroMemory(ptr, size);
        }

        bool SaveConfigValue(const std::string& key, const std::string& value) {
            // Simple INI-style config file implementation
            std::string configPath = CombinePaths(GetExecutableDirectory(), "config.ini");
            
            // Read existing config
            std::map<std::string, std::string> config;
            std::ifstream inFile(configPath);
            std::string line;
            
            while (std::getline(inFile, line)) {
                size_t pos = line.find('=');
                if (pos != std::string::npos) {
                    std::string k = Trim(line.substr(0, pos));
                    std::string v = Trim(line.substr(pos + 1));
                    config[k] = v;
                }
            }
            inFile.close();
            
            // Update value
            config[key] = value;
            
            // Write back to file
            std::ofstream outFile(configPath);
            for (const auto& pair : config) {
                outFile << pair.first << "=" << pair.second << "\n";
            }
            
            return outFile.good();
        }

        std::string LoadConfigValue(const std::string& key, const std::string& defaultValue) {
            std::string configPath = CombinePaths(GetExecutableDirectory(), "config.ini");
            std::ifstream file(configPath);
            std::string line;
            
            while (std::getline(file, line)) {
                size_t pos = line.find('=');
                if (pos != std::string::npos) {
                    std::string k = Trim(line.substr(0, pos));
                    if (k == key) {
                        return Trim(line.substr(pos + 1));
                    }
                }
            }
            
            return defaultValue;
        }

        std::string GetLastErrorString() {
            DWORD error = GetLastError();
            if (error == 0) return "";
            
            LPSTR messageBuffer = nullptr;
            size_t size = FormatMessageA(
                FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                nullptr, error, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                (LPSTR)&messageBuffer, 0, nullptr);
            
            std::string message(messageBuffer, size);
            LocalFree(messageBuffer);
            
            return Trim(message);
        }

        void LogError(const std::string& message) {
            std::string logPath = CombinePaths(GetExecutableDirectory(), "error.log");
            std::ofstream file(logPath, std::ios::app);
            file << GetCurrentTimeString() << " [ERROR] " << message << std::endl;
        }

        void LogInfo(const std::string& message) {
            std::string logPath = CombinePaths(GetExecutableDirectory(), "info.log");
            std::ofstream file(logPath, std::ios::app);
            file << GetCurrentTimeString() << " [INFO] " << message << std::endl;
        }
    }
}
