#include "Encryption.h"
#include <windows.h>
#include <wincrypt.h>
#include <bcrypt.h>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <random>

#pragma comment(lib, "bcrypt.lib")
#pragma comment(lib, "crypt32.lib")

namespace Toolbox {
    std::vector<uint8_t> Encryption::GenerateSalt(size_t length) {
        return GenerateRandomBytes(length);
    }

    std::vector<uint8_t> Encryption::DeriveKey(const std::string& password, 
                                             const std::vector<uint8_t>& salt,
                                             int iterations) {
        std::vector<uint8_t> key(32); // 256 bits
        
        BCRYPT_ALG_HANDLE hAlg = nullptr;
        BCRYPT_HASH_HANDLE hHash = nullptr;
        
        NTSTATUS status = BCryptOpenAlgorithmProvider(&hAlg, BCRYPT_PBKDF2_ALGORITHM, nullptr, 0);
        if (!BCRYPT_SUCCESS(status)) {
            return key;
        }
        
        status = BCryptDeriveKeyPBKDF2(hAlg,
                                      (PUCHAR)password.c_str(), (ULONG)password.length(),
                                      (PUCHAR)salt.data(), (ULONG)salt.size(),
                                      iterations,
                                      key.data(), (ULONG)key.size(),
                                      0);
        
        BCryptCloseAlgorithmProvider(hAlg, 0);
        return key;
    }

    std::vector<uint8_t> Encryption::Encrypt(const std::string& plaintext,
                                            const std::vector<uint8_t>& key,
                                            const std::vector<uint8_t>& iv) {
        std::vector<uint8_t> result;
        
        BCRYPT_ALG_HANDLE hAlg = nullptr;
        BCRYPT_KEY_HANDLE hKey = nullptr;
        
        NTSTATUS status = BCryptOpenAlgorithmProvider(&hAlg, BCRYPT_AES_ALGORITHM, nullptr, 0);
        if (!BCRYPT_SUCCESS(status)) return result;
        
        status = BCryptSetProperty(hAlg, BCRYPT_CHAINING_MODE, 
                                  (PUCHAR)BCRYPT_CHAIN_MODE_CBC, 
                                  sizeof(BCRYPT_CHAIN_MODE_CBC), 0);
        if (!BCRYPT_SUCCESS(status)) {
            BCryptCloseAlgorithmProvider(hAlg, 0);
            return result;
        }
        
        status = BCryptGenerateSymmetricKey(hAlg, &hKey, nullptr, 0,
                                           (PUCHAR)key.data(), (ULONG)key.size(), 0);
        if (!BCRYPT_SUCCESS(status)) {
            BCryptCloseAlgorithmProvider(hAlg, 0);
            return result;
        }
        
        DWORD cbResult = 0;
        std::vector<uint8_t> ivCopy = iv; // BCrypt modifies IV
        
        status = BCryptEncrypt(hKey, (PUCHAR)plaintext.c_str(), (ULONG)plaintext.length(),
                              nullptr, ivCopy.data(), (ULONG)ivCopy.size(),
                              nullptr, 0, &cbResult, BCRYPT_BLOCK_PADDING);
        
        if (BCRYPT_SUCCESS(status)) {
            result.resize(cbResult);
            ivCopy = iv; // Reset IV
            status = BCryptEncrypt(hKey, (PUCHAR)plaintext.c_str(), (ULONG)plaintext.length(),
                                  nullptr, ivCopy.data(), (ULONG)ivCopy.size(),
                                  result.data(), cbResult, &cbResult, BCRYPT_BLOCK_PADDING);
        }
        
        BCryptDestroyKey(hKey);
        BCryptCloseAlgorithmProvider(hAlg, 0);
        
        return result;
    }

    std::string Encryption::Decrypt(const std::vector<uint8_t>& ciphertext,
                                   const std::vector<uint8_t>& key,
                                   const std::vector<uint8_t>& iv) {
        std::string result;
        
        BCRYPT_ALG_HANDLE hAlg = nullptr;
        BCRYPT_KEY_HANDLE hKey = nullptr;
        
        NTSTATUS status = BCryptOpenAlgorithmProvider(&hAlg, BCRYPT_AES_ALGORITHM, nullptr, 0);
        if (!BCRYPT_SUCCESS(status)) return result;
        
        status = BCryptSetProperty(hAlg, BCRYPT_CHAINING_MODE, 
                                  (PUCHAR)BCRYPT_CHAIN_MODE_CBC, 
                                  sizeof(BCRYPT_CHAIN_MODE_CBC), 0);
        if (!BCRYPT_SUCCESS(status)) {
            BCryptCloseAlgorithmProvider(hAlg, 0);
            return result;
        }
        
        status = BCryptGenerateSymmetricKey(hAlg, &hKey, nullptr, 0,
                                           (PUCHAR)key.data(), (ULONG)key.size(), 0);
        if (!BCRYPT_SUCCESS(status)) {
            BCryptCloseAlgorithmProvider(hAlg, 0);
            return result;
        }
        
        DWORD cbResult = 0;
        std::vector<uint8_t> ivCopy = iv; // BCrypt modifies IV
        
        status = BCryptDecrypt(hKey, (PUCHAR)ciphertext.data(), (ULONG)ciphertext.size(),
                              nullptr, ivCopy.data(), (ULONG)ivCopy.size(),
                              nullptr, 0, &cbResult, BCRYPT_BLOCK_PADDING);
        
        if (BCRYPT_SUCCESS(status)) {
            std::vector<uint8_t> plaintext(cbResult);
            ivCopy = iv; // Reset IV
            status = BCryptDecrypt(hKey, (PUCHAR)ciphertext.data(), (ULONG)ciphertext.size(),
                                  nullptr, ivCopy.data(), (ULONG)ivCopy.size(),
                                  plaintext.data(), cbResult, &cbResult, BCRYPT_BLOCK_PADDING);
            
            if (BCRYPT_SUCCESS(status)) {
                result = std::string(plaintext.begin(), plaintext.begin() + cbResult);
            }
        }
        
        BCryptDestroyKey(hKey);
        BCryptCloseAlgorithmProvider(hAlg, 0);
        
        return result;
    }

    std::vector<uint8_t> Encryption::GenerateIV() {
        return GenerateRandomBytes(16); // AES block size
    }

    std::string Encryption::HashPassword(const std::string& password, 
                                        const std::vector<uint8_t>& salt) {
        BCRYPT_ALG_HANDLE hAlg = nullptr;
        BCRYPT_HASH_HANDLE hHash = nullptr;
        
        NTSTATUS status = BCryptOpenAlgorithmProvider(&hAlg, BCRYPT_SHA256_ALGORITHM, nullptr, 0);
        if (!BCRYPT_SUCCESS(status)) return "";
        
        DWORD hashLength = 0;
        DWORD resultLength = 0;
        status = BCryptGetProperty(hAlg, BCRYPT_HASH_LENGTH, 
                                  (PUCHAR)&hashLength, sizeof(hashLength), &resultLength, 0);
        if (!BCRYPT_SUCCESS(status)) {
            BCryptCloseAlgorithmProvider(hAlg, 0);
            return "";
        }
        
        status = BCryptCreateHash(hAlg, &hHash, nullptr, 0, nullptr, 0, 0);
        if (!BCRYPT_SUCCESS(status)) {
            BCryptCloseAlgorithmProvider(hAlg, 0);
            return "";
        }
        
        // Hash password + salt
        BCryptHashData(hHash, (PUCHAR)password.c_str(), (ULONG)password.length(), 0);
        BCryptHashData(hHash, (PUCHAR)salt.data(), (ULONG)salt.size(), 0);
        
        std::vector<uint8_t> hash(hashLength);
        status = BCryptFinishHash(hHash, hash.data(), hashLength, 0);
        
        BCryptDestroyHash(hHash);
        BCryptCloseAlgorithmProvider(hAlg, 0);
        
        return BCRYPT_SUCCESS(status) ? BytesToHex(hash) : "";
    }

    std::vector<uint8_t> Encryption::GenerateRandomBytes(size_t length) {
        std::vector<uint8_t> bytes(length);
        NTSTATUS status = BCryptGenRandom(nullptr, bytes.data(), (ULONG)length, 
                                         BCRYPT_USE_SYSTEM_PREFERRED_RNG);
        return BCRYPT_SUCCESS(status) ? bytes : std::vector<uint8_t>();
    }

    std::string Encryption::BytesToHex(const std::vector<uint8_t>& bytes) {
        std::stringstream ss;
        ss << std::hex << std::setfill('0');
        for (uint8_t byte : bytes) {
            ss << std::setw(2) << static_cast<int>(byte);
        }
        return ss.str();
    }

    std::vector<uint8_t> Encryption::HexToBytes(const std::string& hex) {
        std::vector<uint8_t> bytes;
        for (size_t i = 0; i < hex.length(); i += 2) {
            std::string byteString = hex.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::strtol(byteString.c_str(), nullptr, 16));
            bytes.push_back(byte);
        }
        return bytes;
    }
}
