/*!

@page compile_guide Compiling GLFW

@tableofcontents

This is about compiling the GLFW library itself.  For information on how to
build applications that use GLFW, see @ref build_guide.


@section compile_cmake Using CMake

@note GLFW behaves like most other libraries that use CMake so this guide mostly
describes the basic configure/generate/compile sequence.  If you are already
familiar with this from other projects, you may want to focus on the @ref
compile_deps and @ref compile_options sections for GLFW-specific information.

GLFW uses [CMake](https://cmake.org/) to generate project files or makefiles
for your chosen development environment.  To compile GLFW, first generate these
files with CMake and then use them to compile the GLFW library. 

If you are on Windows and macOS you can
[download CMake](https://cmake.org/download/) from their site.

If you are on a Unix-like system such as Linux, FreeBSD or Cygwin or have
a package system like Fink, MacPorts or Homebrew, you can install its CMake
package.

CMake is a complex tool and this guide will only show a few of the possible ways
to set up and compile GLFW.  The CMake project has their own much more detailed
[CMake user guide](https://cmake.org/cmake/help/latest/guide/user-interaction/)
that includes everything in this guide not specific to GLFW.  It may be a useful
companion to this one.


@subsection compile_deps Installing dependencies

The C/C++ development environments in Visual Studio, Xcode and MinGW come with
all necessary dependencies for compiling GLFW, but on Unix-like systems like
Linux and FreeBSD you will need a few extra packages.


@subsubsection compile_deps_x11 Dependencies for X11 on Unix-like systems

To compile GLFW for X11, you need to have the X11 development packages
installed.  They are not needed to build or run programs that use GLFW.

On Debian and derivates like Ubuntu and Linux Mint the `xorg-dev` meta-package
pulls in the development packages for all of X11.

@code{.sh}
sudo apt install xorg-dev
@endcode

On Fedora and derivatives like Red Hat the X11 extension packages
`libXcursor-devel`, `libXi-devel`, `libXinerama-devel` and `libXrandr-devel`
required by GLFW pull in all its other dependencies.

@code{.sh}
sudo dnf install libXcursor-devel libXi-devel libXinerama-devel libXrandr-devel
@endcode

On FreeBSD the X11 headers are installed along the end-user X11 packages, so if
you have an X server running you should have the headers as well.  If not,
install the `xorgproto` package.

@code{.sh}
pkg install xorgproto
@endcode

On Cygwin the `libXcursor-devel`, `libXi-devel`, `libXinerama-devel`,
`libXrandr-devel` and `libXrender-devel` packages in the Libs section of the GUI
installer will install all the headers and other development related files GLFW
requires for X11.

Once you have the required depdendencies, move on to @ref compile_generate.


@subsubsection compile_deps_wayland Dependencies for Wayland on Unix-like systems

To compile GLFW for Wayland, you need to have the Wayland and xkbcommon
development packages installed.  They are not needed to build or run programs
that use GLFW.

On Debian and derivates like Ubuntu and Linux Mint you will need the `libwayland-dev`,
`libxkbcommon-dev`, `wayland-protocols` and `extra-cmake-modules` packages.

@code{.sh}
sudo apt install libwayland-dev libxkbcommon-dev wayland-protocols extra-cmake-modules
@endcode

On Fedora and derivatives like Red Hat you will need the `wayland-devel`,
`libxkbcommon-devel`, `wayland-protocols-devel` and `extra-cmake-modules` packages.

@code{.sh}
sudo dnf install wayland-devel libxkbcommon-devel wayland-protocols-devel extra-cmake-modules
@endcode

On FreeBSD you will need the `wayland`, `libxkbcommon`, `wayland-protocols` and
`kf5-extra-cmake-modules` packages.

@code{.sh}
pkg install wayland libxkbcommon wayland-protocols kf5-extra-cmake-modules
@endcode

Once you have the required depdendencies, move on to @ref compile_generate.


@subsection compile_generate Generating build files with CMake

Once you have all necessary dependencies it is time to generate the project
files or makefiles for your development environment.  CMake needs two paths for
this:

 - the path to the root directory of the GLFW source tree (not its `src`
   subdirectory)
 - the path to the directory where the generated build files and compiled
   binaries will be placed

If these are the same, it is called an in-tree build, otherwise it is called an
out-of-tree build.

Out-of-tree builds are recommended as they avoid cluttering up the source tree.
They also allow you to have several build directories for different
configurations all using the same source tree.

A common pattern when building a single configuration is to have a build
directory named `build` in the root of the source tree.


@subsubsection compile_generate_gui Generating files with the CMake GUI

Start the CMake GUI and set the paths to the source and build directories
described above.  Then press _Configure_ and _Generate_.

If you wish change any CMake variables in the list, press _Configure_ and then
_Generate_ to have the new values take effect.  The variable list will be
populated after the first configure step.

By default GLFW will use X11 on Linux and other Unix-like systems other
than macOS.  To use Wayland instead, set the `GLFW_USE_WAYLAND` option in the
GLFW section of the variable list, then apply the new value as described above.

Once you have generated the project files or makefiles for your chosen
development environment, move on to @ref compile_compile.


@subsubsection compile_generate_cli Generating files with the CMake command-line tool

To make a build directory, pass the source and build directories to the `cmake`
command.  These can be relative or absolute paths.  The build directory is
created if it doesn't already exist.

@code{.sh}
cmake -S path/to/glfw -B path/to/build
@endcode

It is common to name the build directory `build` and place it in the root of the
source tree when only planning to build a single configuration.

@code{.sh}
cd path/to/glfw
cmake -S . -B build
@endcode

Without other flags these will generate Visual Studio project files on Windows
and makefiles on other platforms.  You can choose other targets using the `-G`
flag.

@code{.sh}
cmake -S path/to/glfw -B path/to/build -G Xcode
@endcode

By default GLFW will use X11 on Linux and other Unix-like systems other
than macOS.  To use Wayland instead, set the `GLFW_USE_WAYLAND` CMake option.

@code{.sh}
cmake -S path/to/glfw -B path/to/build -D GLFW_USE_WAYLAND=1
@endcode

Once you have generated the project files or makefiles for your chosen
development environment, move on to @ref compile_compile.


@subsection compile_compile Compiling the library

You should now have all required dependencies and the project files or makefiles
necessary to compile GLFW.  Go ahead and compile the actual GLFW library with
these files as you would with any other project.

With Visual Studio open `GLFW.sln` and use the Build menu.  With Xcode open
`GLFW.xcodeproj` and use the Project menu.

With Linux, macOS and other forms of Unix, run `make`.

@code{.sh}
cd path/to/build
make
@endcode

With MinGW, it is `mingw32-make`.

@code{.sh}
cd path/to/build
mingw32-make
@endcode

Any CMake build directory can also be built with the `cmake` command and the
`--build` flag.

@code{.sh}
cmake --build path/to/build
@endcode

This will run the platform specific build tool the directory was generated for.

Once the GLFW library is compiled you are ready to build your application,
linking it to the GLFW library.  See @ref build_guide for more information.


@section compile_options CMake options

The CMake files for GLFW provide a number of options, although not all are
available on all supported platforms.  Some of these are de facto standards
among projects using CMake and so have no `GLFW_` prefix.

If you are using the GUI version of CMake, these are listed and can be changed
from there.  If you are using the command-line version of CMake you can use the
`ccmake` ncurses GUI to set options.  Some package systems like Ubuntu and other
distributions based on Debian GNU/Linux have this tool in a separate
`cmake-curses-gui` package.

Finally, if you don't want to use any GUI, you can set options from the `cmake`
command-line with the `-D` flag.

@code{.sh}
cmake -S path/to/glfw -B path/to/build -D BUILD_SHARED_LIBS=ON
@endcode


@subsection compile_options_shared Shared CMake options

@anchor BUILD_SHARED_LIBS
__BUILD_SHARED_LIBS__ determines whether GLFW is built as a static
library or as a DLL / shared library / dynamic library.  This is disabled by
default, producing a static GLFW library.

@anchor GLFW_BUILD_EXAMPLES
__GLFW_BUILD_EXAMPLES__ determines whether the GLFW examples are built
along with the library.

@anchor GLFW_BUILD_TESTS
__GLFW_BUILD_TESTS__ determines whether the GLFW test programs are
built along with the library.

@anchor GLFW_BUILD_DOCS
__GLFW_BUILD_DOCS__ determines whether the GLFW documentation is built along
with the library.  This is enabled by default if
[Doxygen](https://www.doxygen.nl/) is found by CMake during configuration.

@anchor GLFW_VULKAN_STATIC
__GLFW_VULKAN_STATIC__ determines whether to use the Vulkan loader linked
directly with the application.  This is disabled by default.


@subsection compile_options_win32 Windows specific CMake options

@anchor USE_MSVC_RUNTIME_LIBRARY_DLL
__USE_MSVC_RUNTIME_LIBRARY_DLL__ determines whether to use the DLL version or the
static library version of the Visual C++ runtime library.  When enabled, the
DLL version of the Visual C++ library is used.  This is enabled by default.

On CMake 3.15 and later you can set the standard CMake
[CMAKE_MSVC_RUNTIME_LIBRARY](https://cmake.org/cmake/help/latest/variable/CMAKE_MSVC_RUNTIME_LIBRARY.html)
variable instead of this GLFW-specific option.

@anchor GLFW_USE_HYBRID_HPG
__GLFW_USE_HYBRID_HPG__ determines whether to export the `NvOptimusEnablement` and
`AmdPowerXpressRequestHighPerformance` symbols, which force the use of the
high-performance GPU on Nvidia Optimus and AMD PowerXpress systems.  These symbols
need to be exported by the EXE to be detected by the driver, so the override
will not work if GLFW is built as a DLL.  This is disabled by default, letting
the operating system and driver decide.


@subsection compile_options_wayland Wayland specific CMake options

@anchor GLFW_USE_WAYLAND
__GLFW_USE_WAYLAND__ determines whether to compile the library for Wayland.
This option is only available on Linux and other Unix-like systems other than
macOS.  This is disabled by default.


@section compile_mingw_cross Cross-compilation with CMake and MinGW

Both Cygwin and many Linux distributions have MinGW or MinGW-w64 packages.  For
example, Cygwin has the `mingw64-i686-gcc` and `mingw64-x86_64-gcc` packages
for 32- and 64-bit version of MinGW-w64, while Debian GNU/Linux and derivatives
like Ubuntu have the `mingw-w64` package for both.

GLFW has CMake toolchain files in the `CMake` subdirectory that set up
cross-compilation of Windows binaries.  To use these files you set the
`CMAKE_TOOLCHAIN_FILE` CMake variable with the `-D` flag add an option when
configuring and generating the build files.

@code{.sh}
cmake -S path/to/glfw -B path/to/build -D CMAKE_TOOLCHAIN_FILE=path/to/file
@endcode

The exact toolchain file to use depends on the prefix used by the MinGW or
MinGW-w64 binaries on your system.  You can usually see this in the /usr
directory.  For example, both the Ubuntu and Cygwin MinGW-w64 packages have
`/usr/x86_64-w64-mingw32` for the 64-bit compilers, so the correct invocation
would be:

@code{.sh}
cmake -S path/to/glfw -B path/to/build -D CMAKE_TOOLCHAIN_FILE=CMake/x86_64-w64-mingw32.cmake
@endcode

The path to the toolchain file is relative to the path to the GLFW source tree
passed to the `-S` flag, not to the current directory.

For more details see the
[CMake toolchain guide](https://cmake.org/cmake/help/latest/manual/cmake-toolchains.7.html).


@section compile_manual Compiling GLFW manually

If you wish to compile GLFW without its CMake build environment then you will
have to do at least some of the platform detection yourself.  GLFW needs
a configuration macro to be defined in order to know what window system it is
being compiled for and also has optional, platform-specific ones for various
features.

When building with CMake, the `glfw_config.h` configuration header is generated
based on the current platform and CMake options.  The GLFW CMake environment
defines @b GLFW_USE_CONFIG_H, which causes this header to be included by
`internal.h`.  Without this macro, GLFW will expect the necessary configuration
macros to be defined on the command-line.

The window creation API is used to create windows, handle input, monitors, gamma
ramps and clipboard.  The options are:

 - @b _GLFW_COCOA to use the Cocoa frameworks
 - @b _GLFW_WIN32 to use the Win32 API
 - @b _GLFW_X11 to use the X Window System
 - @b _GLFW_WAYLAND to use the Wayland API (experimental and incomplete)
 - @b _GLFW_OSMESA to use the OSMesa API (headless and non-interactive)

If you are building GLFW as a shared library / dynamic library / DLL then you
must also define @b _GLFW_BUILD_DLL.  Otherwise, you must not define it.

If you are linking the Vulkan loader directly with your application then you
must also define @b _GLFW_VULKAN_STATIC.  Otherwise, GLFW will attempt to use the
external version.

If you are using a custom name for the Vulkan, EGL, GLX, OSMesa, OpenGL, GLESv1
or GLESv2 library, you can override the default names by defining those you need
of @b _GLFW_VULKAN_LIBRARY, @b _GLFW_EGL_LIBRARY, @b _GLFW_GLX_LIBRARY, @b
_GLFW_OSMESA_LIBRARY, @b _GLFW_OPENGL_LIBRARY, @b _GLFW_GLESV1_LIBRARY and @b
_GLFW_GLESV2_LIBRARY.  Otherwise, GLFW will use the built-in default names.

@note None of the @ref build_macros may be defined during the compilation of
GLFW.  If you define any of these in your build files, make sure they are not
applied to the GLFW sources.

*/
