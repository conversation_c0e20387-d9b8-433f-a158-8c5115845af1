#pragma once

#include <string>
#include <vector>
#include <functional>
#include <atomic>

#ifndef TOOLBOX_MINIMAL_BUILD
#include <thread>
#include <mutex>
#endif

namespace Toolbox {
    struct SearchResult {
        std::string filePath;
        std::string fileName;
        std::string directory;
        long long fileSize;
        long long modifiedTime;
        std::vector<std::string> matchingLines;
        std::vector<int> lineNumbers;
        int totalMatches;
        
        SearchResult() : fileSize(0), modifiedTime(0), totalMatches(0) {}
    };

    struct SearchOptions {
        bool caseSensitive = false;
        bool wholeWordsOnly = false;
        bool useRegex = false;
        bool searchInContent = true;
        bool searchInFilenames = true;
        bool includeSubdirectories = true;
        int maxResults = 1000;
        int maxFileSize = 10 * 1024 * 1024; // 10MB
        std::vector<std::string> fileExtensions = {".txt"}; // Default to .txt files
        
        SearchOptions() = default;
    };

    class FileSearcher {
    public:
        FileSearcher();
        ~FileSearcher();

        // Search operations
        void StartSearch(const std::string& searchTerm, 
                        const std::string& searchPath,
                        const SearchOptions& options = SearchOptions());
        
        void StopSearch();
        bool IsSearching() const { return m_isSearching; }
        
        // Results
        std::vector<SearchResult> GetResults() const;
        int GetResultCount() const;
        std::string GetSearchStatus() const;
        float GetSearchProgress() const { return m_searchProgress; }
        
        // Callbacks
        void SetProgressCallback(std::function<void(float, const std::string&)> callback);
        void SetResultCallback(std::function<void(const SearchResult&)> callback);
        void SetCompletedCallback(std::function<void(bool)> callback);
        
        // File operations
        std::string GetFilePreview(const std::string& filePath, int maxLines = 10) const;
        bool OpenFileInDefaultEditor(const std::string& filePath) const;
        std::string GetFileInfo(const std::string& filePath) const;
        
        // Utility functions
        static std::string FormatFileSize(long long bytes);
        static std::string FormatDateTime(long long timestamp);
        static std::vector<std::string> GetCommonTextExtensions();
        
    private:
        std::atomic<bool> m_isSearching;
        std::atomic<float> m_searchProgress;

        #ifndef TOOLBOX_MINIMAL_BUILD
        std::thread m_searchThread;
        mutable std::mutex m_resultsMutex;
        #endif
        
        std::vector<SearchResult> m_results;
        std::string m_currentStatus;
        
        // Callbacks
        std::function<void(float, const std::string&)> m_progressCallback;
        std::function<void(const SearchResult&)> m_resultCallback;
        std::function<void(bool)> m_completedCallback;
        
        // Search implementation
        void SearchThreadFunction(const std::string& searchTerm,
                                 const std::string& searchPath,
                                 const SearchOptions& options);
        
        void SearchDirectory(const std::string& directory,
                           const std::string& searchTerm,
                           const SearchOptions& options,
                           std::atomic<int>& filesProcessed,
                           int totalFiles);
        
        bool SearchInFile(const std::string& filePath,
                         const std::string& searchTerm,
                         const SearchOptions& options,
                         SearchResult& result);
        
        bool MatchesPattern(const std::string& text,
                          const std::string& pattern,
                          const SearchOptions& options) const;
        
        std::vector<std::string> GetFilesInDirectory(const std::string& directory,
                                                   const SearchOptions& options) const;
        
        int CountFilesRecursively(const std::string& directory,
                                const SearchOptions& options) const;
        
        bool ShouldProcessFile(const std::string& filePath,
                             const SearchOptions& options) const;
        
        void UpdateProgress(float progress, const std::string& status);
        void AddResult(const SearchResult& result);
        void NotifyCompleted(bool success);
        
        // File system utilities
        bool IsTextFile(const std::string& filePath) const;
        std::string GetFileExtension(const std::string& filePath) const;
        long long GetFileSize(const std::string& filePath) const;
        long long GetFileModifiedTime(const std::string& filePath) const;
    };
}
