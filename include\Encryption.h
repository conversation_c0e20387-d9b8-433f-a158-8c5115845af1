#pragma once

#include <string>
#include <vector>
#include <cstdint>

namespace Toolbox {
    class Encryption {
    public:
        // Generate a random salt
        static std::vector<uint8_t> GenerateSalt(size_t length = 16);
        
        // Generate a key from password using PBKDF2
        static std::vector<uint8_t> Derive<PERSON>ey(const std::string& password, 
                                            const std::vector<uint8_t>& salt,
                                            int iterations = 10000);
        
        // AES-256-CBC encryption
        static std::vector<uint8_t> Encrypt(const std::string& plaintext,
                                          const std::vector<uint8_t>& key,
                                          const std::vector<uint8_t>& iv);
        
        // AES-256-CBC decryption
        static std::string Decrypt(const std::vector<uint8_t>& ciphertext,
                                 const std::vector<uint8_t>& key,
                                 const std::vector<uint8_t>& iv);
        
        // Generate random IV
        static std::vector<uint8_t> GenerateIV();
        
        // Hash password for verification (SHA-256)
        static std::string HashPassword(const std::string& password, 
                                      const std::vector<uint8_t>& salt);
        
        // Secure random bytes
        static std::vector<uint8_t> GenerateRandomBytes(size_t length);
        
        // Convert bytes to hex string
        static std::string BytesToHex(const std::vector<uint8_t>& bytes);
        
        // Convert hex string to bytes
        static std::vector<uint8_t> HexToBytes(const std::string& hex);
        
    private:
        // Windows CryptoAPI helper functions
        static bool InitializeCrypto();
        static void CleanupCrypto();
    };
}
